#!/usr/bin/env python3
"""
调试重合度排行榜详细信息中未来排名数据不一致的问题
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_ranking_issue():
    """测试排名问题"""
    print("🔍 测试重合度排行榜详细信息中的排名问题")
    print("=" * 60)
    
    # 模拟future_rankings数据结构
    print("\n1. 模拟未来收益率排行榜数据:")
    future_rankings = {
        30: pd.DataFrame({
            'rank': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
            'sector_code': ['BK0427', 'BK0478', 'BK0546', 'BK0729', 'BK1015', 'BK0424', 'BK0479', 'BK0739', 'BK0727', 'BK1027', 'BK0999'],
            'sector_name': ['煤炭行业', '钢铁行业', '玻璃玻纤', '船舶制造', '能源金属', '水泥建材', '有色金属', '工程机械', '医疗服务', '小金属', '其他行业'],
            'avg_return_pct': [8.5, 6.2, 5.8, 5.2, 4.8, 4.5, 4.2, 3.9, 3.6, 3.3, 3.0]
        }),
        60: pd.DataFrame({
            'rank': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
            'sector_code': ['BK0424', 'BK0739', 'BK0727', 'BK0427', 'BK1027', 'BK0478', 'BK0546', 'BK0729', 'BK1015', 'BK0479', 'BK0999'],
            'sector_name': ['水泥建材', '工程机械', '医疗服务', '煤炭行业', '小金属', '钢铁行业', '玻璃玻纤', '船舶制造', '能源金属', '有色金属', '其他行业'],
            'avg_return_pct': [7.2, 6.8, 5.5, 5.1, 4.9, 4.6, 4.3, 4.0, 3.7, 3.4, 3.1]
        })
    }
    
    # 显示数据
    for window_days, rankings in future_rankings.items():
        print(f"\n未来{window_days}日排行榜:")
        for i, (idx, sector) in enumerate(rankings.head(5).iterrows()):
            print(f"  DataFrame索引{idx}: {sector['sector_name']} - 实际排名{sector['rank']} - 收益率{sector['avg_return_pct']:.1f}%")
    
    print("\n2. 当前错误的排名获取方式:")
    print("   使用 idx + 1 (DataFrame索引位置)")
    
    # 模拟当前错误的逻辑
    coal_sector_details_wrong = []
    for window_days, rankings in future_rankings.items():
        if not rankings.empty:
            top_sectors = rankings.head(10)
            for idx, sector in top_sectors.iterrows():
                if sector['sector_code'] == 'BK0427':  # 煤炭行业
                    wrong_rank = idx + 1  # 错误：使用DataFrame索引
                    correct_rank = sector['rank']  # 正确：使用rank列
                    coal_sector_details_wrong.append({
                        'window_days': window_days,
                        'wrong_rank': wrong_rank,
                        'correct_rank': correct_rank,
                        'avg_return_pct': sector['avg_return_pct']
                    })
    
    print("\n3. 煤炭行业排名对比:")
    for detail in coal_sector_details_wrong:
        print(f"   未来{detail['window_days']}日: 错误排名第{detail['wrong_rank']}名 vs 正确排名第{detail['correct_rank']}名 ({detail['avg_return_pct']:.1f}%)")
    
    print("\n4. 问题分析:")
    print("   ❌ 错误原因: 在report_generator.py第1249行使用了 'rank': idx + 1")
    print("   ❌ idx是DataFrame的索引位置，不是实际排名")
    print("   ✅ 正确方式: 应该使用 'rank': sector['rank'] 或 sector.get('rank', idx + 1)")
    
    print("\n5. 修复建议:")
    print("   将第1249行的 'rank': idx + 1 改为 'rank': sector.get('rank', idx + 1)")
    
    return True

if __name__ == "__main__":
    test_ranking_issue()
