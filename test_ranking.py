import pandas as pd

# 模拟数据
df = pd.DataFrame({
    'rank': [1, 2, 3, 4, 5],
    'sector_code': ['BK0427', 'BK0478', 'BK0546', 'BK0729', 'BK1015'],
    'sector_name': ['煤炭行业', '钢铁行业', '玻璃玻纤', '船舶制造', '能源金属'],
    'avg_return_pct': [8.5, 6.2, 5.8, 5.2, 4.8]
})

print("DataFrame:")
print(df)
print()

print("遍历DataFrame:")
for idx, sector in df.iterrows():
    print(f"索引{idx}: {sector['sector_name']} - 实际排名{sector['rank']}")
    print(f"  错误方式(idx+1): 第{idx+1}名")
    print(f"  正确方式(rank): 第{sector['rank']}名")
    print()
