#!/usr/bin/env python3
"""
验证重合度排行榜详细信息排名修复的完整性
"""

import re
import os

def verify_ranking_fix():
    """验证排名修复的完整性"""
    print("🔍 验证重合度排行榜详细信息排名修复完整性")
    print("=" * 60)
    
    # 读取修复后的文件
    file_path = os.path.join('src', 'report_generator.py')
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在:", file_path)
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n1. 检查修复点:")
    
    # 检查所有修复点
    fix_patterns = [
        (r"actual_rank = sector\.get\('rank', idx \+ 1\)", "future_return_ranking"),
        (r"actual_rank = sector\.get\('rank', idx \+ 1\)", "future_consistency_ranking"),
        (r"actual_rank = sector\.get\('rank', idx \+ 1\)", "champion_count"),
        (r"actual_rank = sector\.get\('rank', idx \+ 1\)", "frequency_ranking"),
        (r"actual_rank = sector_info\.get\('rank', idx \+ 1\)", "historical_consistency_ranking"),
        (r"actual_rank = sector\.get\('rank', idx \+ 1\)", "historical_performance")
    ]
    
    # 统计修复点
    actual_rank_count = len(re.findall(r"actual_rank = .*\.get\('rank', idx \+ 1\)", content))
    print(f"  ✅ 找到 {actual_rank_count} 个 actual_rank 修复点")
    
    # 检查是否还有未修复的 'rank': idx + 1 模式
    old_pattern_matches = re.findall(r"'rank':\s*idx\s*\+\s*1", content)
    if old_pattern_matches:
        print(f"  ❌ 仍有 {len(old_pattern_matches)} 个未修复的 'rank': idx + 1")
        return False
    else:
        print("  ✅ 所有 'rank': idx + 1 都已修复")
    
    # 检查是否正确使用了 actual_rank
    rank_usage_matches = re.findall(r"'rank':\s*actual_rank", content)
    print(f"  ✅ 找到 {len(rank_usage_matches)} 个正确的 'rank': actual_rank 使用")
    
    # 检查评分计算是否也使用了 actual_rank
    score_calc_matches = re.findall(r"rank_score = max\(0, 11 - actual_rank\)", content)
    print(f"  ✅ 找到 {len(score_calc_matches)} 个正确的评分计算")
    
    print("\n2. 检查修复的数据类型:")
    
    # 检查各种数据类型的修复
    data_types = [
        ("future_return_ranking", "未来收益率排名"),
        ("future_consistency_ranking", "未来一致性排名"),
        ("champion_count", "历史冠军次数"),
        ("frequency_ranking", "历史频次排名"),
        ("historical_consistency_ranking", "历史一致性排名"),
        ("historical_performance", "历史表现排名")
    ]
    
    for data_type, description in data_types:
        if data_type in content:
            print(f"  ✅ {description} 数据类型存在")
        else:
            print(f"  ⚠️ {description} 数据类型未找到")
    
    print("\n3. 验证修复逻辑:")
    
    # 检查修复逻辑的完整性
    expected_patterns = [
        r"# 使用正确的排名：优先使用rank列，否则使用索引位置",
        r"actual_rank = .*\.get\('rank', idx \+ 1\)",
        r"rank_score = max\(0, 11 - actual_rank\)",
        r"'rank': actual_rank"
    ]
    
    all_patterns_found = True
    for pattern in expected_patterns:
        matches = re.findall(pattern, content)
        if matches:
            print(f"  ✅ 模式匹配: {pattern[:50]}... ({len(matches)} 次)")
        else:
            print(f"  ❌ 模式缺失: {pattern[:50]}...")
            all_patterns_found = False
    
    print("\n4. 修复完整性评估:")
    
    if all_patterns_found and actual_rank_count >= 6 and len(old_pattern_matches) == 0:
        print("  🎉 修复完整性验证通过！")
        print("  📊 所有排名获取逻辑都已正确修复")
        print("  🔧 详细信息将显示正确的排名数据")
        return True
    else:
        print("  ❌ 修复完整性验证失败")
        print("  🔧 请检查是否有遗漏的修复点")
        return False

if __name__ == "__main__":
    verify_ranking_fix()
